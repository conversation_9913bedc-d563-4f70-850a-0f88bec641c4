/**
 * 统一日志管理器 - 解决控制台spam问题
 * 🎯 核心价值：统一管理所有调试日志输出，提供精确的控制机制
 * 🚀 主要功能：
 *   - 日志级别控制（ERROR, WARN, INFO, DEBUG）
 *   - 全局开关控制
 *   - 坐标过滤功能
 *   - 功能模块过滤
 *   - 与现有sessionStorage调试机制兼容
 * 
 * 使用方式：
 *   import { logger } from '@/utils/LogManager';
 *   logger.debug('调试信息', { data: someData });
 *   logger.info('一般信息');
 *   logger.warn('警告信息');
 *   logger.error('错误信息');
 */

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

export enum LogCategory {
  GENERAL = 'general',
  COLOR = 'color',
  GROUP = 'group',
  CELL = 'cell',
  RENDER = 'render',
  INTERACTION = 'interaction',
  PERFORMANCE = 'performance'
}

export interface LogConfig {
  enabled: boolean;
  level: LogLevel;
  categories: Set<LogCategory>;
  coordinateFilter?: string; // 格式: "x,y"
  moduleFilter?: string[];
}

export interface LogEntry {
  timestamp: number;
  level: LogLevel;
  category: LogCategory;
  message: string;
  data?: any;
  coordinate?: string;
  module?: string;
}

class LogManager {
  private static instance: LogManager;
  private config: LogConfig;
  private history: LogEntry[] = [];
  private maxHistorySize = 1000;

  private constructor() {
    this.config = this.loadConfig();
    this.setupGlobalAccess();
  }

  public static getInstance(): LogManager {
    if (!LogManager.instance) {
      LogManager.instance = new LogManager();
    }
    return LogManager.instance;
  }

  /**
   * 加载配置（兼容现有sessionStorage机制）
   */
  private loadConfig(): LogConfig {
    const isClient = typeof window !== 'undefined';
    
    return {
      enabled: isClient ? 
        sessionStorage.getItem('debug-enabled') === 'true' : false,
      level: isClient ? 
        parseInt(sessionStorage.getItem('debug-level') || '1') : LogLevel.WARN,
      categories: new Set([
        LogCategory.GENERAL,
        LogCategory.COLOR,
        LogCategory.GROUP,
        LogCategory.CELL
      ]),
      coordinateFilter: isClient ? 
        sessionStorage.getItem('debug-coords-filter') || undefined : undefined,
      moduleFilter: isClient ? 
        JSON.parse(sessionStorage.getItem('debug-module-filter') || '[]') : []
    };
  }

  /**
   * 保存配置到sessionStorage
   */
  private saveConfig(): void {
    if (typeof window === 'undefined') return;
    
    sessionStorage.setItem('debug-enabled', this.config.enabled.toString());
    sessionStorage.setItem('debug-level', this.config.level.toString());
    sessionStorage.setItem('debug-coords-filter', this.config.coordinateFilter || '');
    sessionStorage.setItem('debug-module-filter', JSON.stringify(this.config.moduleFilter || []));
  }

  /**
   * 设置全局访问（兼容现有debugHelper）
   */
  private setupGlobalAccess(): void {
    if (typeof window !== 'undefined') {
      (window as any).logger = this;
      (window as any).logManager = this;
    }
  }

  /**
   * 检查是否应该输出日志
   */
  private shouldLog(level: LogLevel, category: LogCategory, coordinate?: string, module?: string): boolean {
    // 全局开关检查
    if (!this.config.enabled) return false;
    
    // 日志级别检查
    if (level > this.config.level) return false;
    
    // 分类检查
    if (!this.config.categories.has(category)) return false;
    
    // 坐标过滤检查
    if (this.config.coordinateFilter && coordinate && coordinate !== this.config.coordinateFilter) {
      return false;
    }
    
    // 模块过滤检查
    if (this.config.moduleFilter && this.config.moduleFilter.length > 0 && module) {
      if (!this.config.moduleFilter.includes(module)) return false;
    }
    
    return true;
  }

  /**
   * 核心日志输出方法
   */
  private log(level: LogLevel, category: LogCategory, message: string, data?: any, coordinate?: string, module?: string): void {
    if (!this.shouldLog(level, category, coordinate, module)) return;
    
    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      category,
      message,
      data,
      coordinate,
      module
    };
    
    // 添加到历史记录
    this.history.push(entry);
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    }
    
    // 输出到控制台
    const prefix = this.getLogPrefix(level, category, coordinate, module);
    const consoleMethod = this.getConsoleMethod(level);
    
    if (data) {
      consoleMethod(`${prefix} ${message}`, data);
    } else {
      consoleMethod(`${prefix} ${message}`);
    }
  }

  /**
   * 获取日志前缀
   */
  private getLogPrefix(level: LogLevel, category: LogCategory, coordinate?: string, module?: string): string {
    const levelIcon = ['🚨', '⚠️', 'ℹ️', '🐛'][level];
    const categoryIcon = {
      [LogCategory.GENERAL]: '📋',
      [LogCategory.COLOR]: '🎨',
      [LogCategory.GROUP]: '👥',
      [LogCategory.CELL]: '🔲',
      [LogCategory.RENDER]: '🖼️',
      [LogCategory.INTERACTION]: '👆',
      [LogCategory.PERFORMANCE]: '⚡'
    }[category];
    
    let prefix = `${levelIcon}${categoryIcon}`;
    
    if (coordinate) prefix += ` [${coordinate}]`;
    if (module) prefix += ` {${module}}`;
    
    return prefix;
  }

  /**
   * 获取控制台输出方法
   */
  private getConsoleMethod(level: LogLevel): (...args: any[]) => void {
    switch (level) {
      case LogLevel.ERROR: return console.error;
      case LogLevel.WARN: return console.warn;
      case LogLevel.INFO: return console.info;
      case LogLevel.DEBUG: return console.log;
      default: return console.log;
    }
  }

  // ==================== 公共API ====================

  /**
   * 错误日志
   */
  public error(message: string, data?: any, coordinate?: string, module?: string): void {
    this.log(LogLevel.ERROR, LogCategory.GENERAL, message, data, coordinate, module);
  }

  /**
   * 警告日志
   */
  public warn(message: string, data?: any, coordinate?: string, module?: string): void {
    this.log(LogLevel.WARN, LogCategory.GENERAL, message, data, coordinate, module);
  }

  /**
   * 信息日志
   */
  public info(message: string, data?: any, coordinate?: string, module?: string): void {
    this.log(LogLevel.INFO, LogCategory.GENERAL, message, data, coordinate, module);
  }

  /**
   * 调试日志
   */
  public debug(message: string, data?: any, coordinate?: string, module?: string): void {
    this.log(LogLevel.DEBUG, LogCategory.GENERAL, message, data, coordinate, module);
  }

  /**
   * 颜色相关日志
   */
  public color(level: LogLevel, message: string, data?: any, coordinate?: string): void {
    this.log(level, LogCategory.COLOR, message, data, coordinate, 'color');
  }

  /**
   * 分组相关日志
   */
  public group(level: LogLevel, message: string, data?: any, coordinate?: string): void {
    this.log(level, LogCategory.GROUP, message, data, coordinate, 'group');
  }

  /**
   * 单元格相关日志
   */
  public cell(level: LogLevel, message: string, data?: any, coordinate?: string): void {
    this.log(level, LogCategory.CELL, message, data, coordinate, 'cell');
  }

  /**
   * 渲染相关日志
   */
  public render(level: LogLevel, message: string, data?: any, coordinate?: string): void {
    this.log(level, LogCategory.RENDER, message, data, coordinate, 'render');
  }

  // ==================== 配置管理 ====================

  /**
   * 启用/禁用日志
   */
  public setEnabled(enabled: boolean): void {
    this.config.enabled = enabled;
    this.saveConfig();
  }

  /**
   * 设置日志级别
   */
  public setLevel(level: LogLevel): void {
    this.config.level = level;
    this.saveConfig();
  }

  /**
   * 设置坐标过滤器
   */
  public setCoordinateFilter(coordinate?: string): void {
    this.config.coordinateFilter = coordinate;
    this.saveConfig();
  }

  /**
   * 设置分类过滤器
   */
  public setCategories(categories: LogCategory[]): void {
    this.config.categories = new Set(categories);
    this.saveConfig();
  }

  /**
   * 获取当前配置
   */
  public getConfig(): LogConfig {
    return { ...this.config };
  }

  /**
   * 获取日志历史
   */
  public getHistory(): LogEntry[] {
    return [...this.history];
  }

  /**
   * 清空日志历史
   */
  public clearHistory(): void {
    this.history = [];
  }

  /**
   * 快速启用调试模式
   */
  public enableDebugMode(): void {
    this.config.enabled = true;
    this.config.level = LogLevel.DEBUG;
    this.config.categories = new Set(Object.values(LogCategory));
    this.saveConfig();
    this.info('调试模式已启用');
  }

  /**
   * 快速禁用调试模式
   */
  public disableDebugMode(): void {
    this.config.enabled = false;
    this.saveConfig();
    console.log('🔇 调试模式已禁用');
  }

  /**
   * 启用精简模式（只显示错误和警告）
   */
  public enableCompactMode(): void {
    this.config.enabled = true;
    this.config.level = LogLevel.WARN;
    this.config.categories = new Set([LogCategory.GENERAL]);
    this.saveConfig();
    this.warn('精简模式已启用，只显示错误和警告');
  }
}

// 导出单例实例
export const logger = LogManager.getInstance();

// 导出类型
export { LogManager };
