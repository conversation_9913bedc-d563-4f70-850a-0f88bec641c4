/**
 * 统一的单元格数据管理器
 * 🎯 核心价值：提供标准化的单元格数据CRUD接口和数据验证
 * 🚀 主要功能：
 *   - 标准化CRUD接口（create, read, update, delete）
 *   - 数据验证和一致性检查方法
 *   - 事件通知机制（数据变更时通知UI更新）
 *   - 颜色映射值和显示颜色的同步管理
 *   - 分组数据的独立控制
 * 
 * 使用方式：
 *   import { cellDataManager } from '@/utils/CellDataManager';
 *   const cell = cellDataManager.getCellData('cell-1');
 *   cellDataManager.updateCellData('cell-1', { color: '#FF0000' });
 */

import { CellData } from '@/types/grid';
import { logger, LogLevel } from './LogManager';

export interface CellDataValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface CellDataUpdateEvent {
  cellId: string;
  oldData: CellData;
  newData: CellData;
  changes: Partial<CellData>;
}

export type CellDataEventListener = (event: CellDataUpdateEvent) => void;

class CellDataManager {
  private static instance: CellDataManager;
  private cellDataMap: Map<string, CellData> = new Map();
  private eventListeners: CellDataEventListener[] = [];
  private validationEnabled = true;

  private constructor() {
    this.setupGlobalAccess();
  }

  public static getInstance(): CellDataManager {
    if (!CellDataManager.instance) {
      CellDataManager.instance = new CellDataManager();
    }
    return CellDataManager.instance;
  }

  /**
   * 设置全局访问
   */
  private setupGlobalAccess(): void {
    if (typeof window !== 'undefined') {
      (window as any).cellDataManager = this;
    }
  }

  // ==================== CRUD接口 ====================

  /**
   * 创建单元格数据
   */
  public createCellData(cellData: CellData): boolean {
    try {
      // 数据验证
      if (this.validationEnabled) {
        const validation = this.validateCellData(cellData);
        if (!validation.isValid) {
          logger.error('创建单元格数据失败：数据验证不通过', validation.errors);
          return false;
        }
      }

      // 检查ID是否已存在
      if (this.cellDataMap.has(cellData.id)) {
        logger.warn('单元格ID已存在，将覆盖现有数据', { id: cellData.id });
      }

      // 确保颜色映射值和显示颜色的一致性
      const processedData = this.ensureColorConsistency(cellData);
      
      this.cellDataMap.set(cellData.id, processedData);
      
      logger.cell(LogLevel.DEBUG, '创建单元格数据成功', { id: cellData.id }, `${cellData.x},${cellData.y}`);
      return true;
    } catch (error) {
      logger.error('创建单元格数据时发生错误', error);
      return false;
    }
  }

  /**
   * 读取单元格数据
   */
  public getCellData(id: string): CellData | null {
    const data = this.cellDataMap.get(id);
    if (!data) {
      logger.cell(LogLevel.DEBUG, '单元格数据不存在', { id });
      return null;
    }
    return { ...data }; // 返回副本，防止外部修改
  }

  /**
   * 更新单元格数据
   */
  public updateCellData(id: string, updates: Partial<CellData>): boolean {
    try {
      const existingData = this.cellDataMap.get(id);
      if (!existingData) {
        logger.error('更新失败：单元格数据不存在', { id });
        return false;
      }

      const newData = { ...existingData, ...updates };

      // 数据验证
      if (this.validationEnabled) {
        const validation = this.validateCellData(newData);
        if (!validation.isValid) {
          logger.error('更新单元格数据失败：数据验证不通过', validation.errors);
          return false;
        }
      }

      // 确保颜色映射值和显示颜色的一致性
      const processedData = this.ensureColorConsistency(newData);

      // 触发事件通知
      const event: CellDataUpdateEvent = {
        cellId: id,
        oldData: existingData,
        newData: processedData,
        changes: updates
      };

      this.cellDataMap.set(id, processedData);
      this.notifyListeners(event);

      logger.cell(LogLevel.DEBUG, '更新单元格数据成功', { id, changes: updates }, `${processedData.x},${processedData.y}`);
      return true;
    } catch (error) {
      logger.error('更新单元格数据时发生错误', error);
      return false;
    }
  }

  /**
   * 删除单元格数据
   */
  public deleteCellData(id: string): boolean {
    try {
      const existed = this.cellDataMap.delete(id);
      if (existed) {
        logger.cell(LogLevel.DEBUG, '删除单元格数据成功', { id });
      } else {
        logger.warn('删除失败：单元格数据不存在', { id });
      }
      return existed;
    } catch (error) {
      logger.error('删除单元格数据时发生错误', error);
      return false;
    }
  }

  // ==================== 查询接口 ====================

  /**
   * 根据坐标获取单元格数据
   */
  public getCellByCoordinate(x: number, y: number): CellData | null {
    for (const cell of this.cellDataMap.values()) {
      if (cell.x === x && cell.y === y) {
        return { ...cell };
      }
    }
    return null;
  }

  /**
   * 根据行列位置获取单元格数据
   */
  public getCellByPosition(row: number, column: number): CellData | null {
    for (const cell of this.cellDataMap.values()) {
      if (cell.row === row && cell.column === column) {
        return { ...cell };
      }
    }
    return null;
  }

  /**
   * 根据分组获取单元格数据
   */
  public getCellsByGroup(groupId: number): CellData[] {
    const cells: CellData[] = [];
    for (const cell of this.cellDataMap.values()) {
      if (cell.group === groupId) {
        cells.push({ ...cell });
      }
    }
    return cells;
  }

  /**
   * 根据级别获取单元格数据
   */
  public getCellsByLevel(level: number): CellData[] {
    const cells: CellData[] = [];
    for (const cell of this.cellDataMap.values()) {
      if (cell.level === level) {
        cells.push({ ...cell });
      }
    }
    return cells;
  }

  /**
   * 批量更新指定级别的单元格
   */
  public updateCellsByLevel(level: number, updates: Partial<CellData>): number {
    let updateCount = 0;
    for (const [id, cell] of this.cellDataMap.entries()) {
      if (cell.level === level) {
        if (this.updateCellData(id, updates)) {
          updateCount++;
        }
      }
    }
    logger.cell(LogLevel.INFO, `批量更新级别${level}的单元格`, { level, updateCount, updates });
    return updateCount;
  }

  // ==================== 数据验证 ====================

  /**
   * 验证单元格数据
   */
  public validateCellData(cellData: CellData): CellDataValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 必填字段检查
    if (!cellData.id) errors.push('id字段不能为空');
    if (typeof cellData.row !== 'number') errors.push('row字段必须是数字');
    if (typeof cellData.column !== 'number') errors.push('column字段必须是数字');
    if (typeof cellData.x !== 'number') errors.push('x字段必须是数字');
    if (typeof cellData.y !== 'number') errors.push('y字段必须是数字');
    if (typeof cellData.index !== 'number') errors.push('index字段必须是数字');
    if (!cellData.color) errors.push('color字段不能为空');
    if (typeof cellData.colorMappingValue !== 'number') errors.push('colorMappingValue字段必须是数字');
    if (typeof cellData.level !== 'number') errors.push('level字段必须是数字');
    if (typeof cellData.group !== 'number') errors.push('group字段必须是数字');

    // 范围检查
    if (cellData.level < 1 || cellData.level > 4) {
      errors.push('level字段必须在1-4之间');
    }

    // 颜色格式检查
    if (cellData.color && !this.isValidColorFormat(cellData.color)) {
      warnings.push('color字段格式可能不正确，建议使用十六进制格式如#FF0000');
    }

    // 分组范围检查
    if (cellData.group < 1 || cellData.group > 44) {
      warnings.push('group字段超出常规范围(1-44)');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 检查颜色格式是否有效
   */
  private isValidColorFormat(color: string): boolean {
    // 检查十六进制颜色格式
    const hexPattern = /^#[0-9A-Fa-f]{6}$/;
    if (hexPattern.test(color)) return true;

    // 检查CSS颜色名称
    const cssColors = ['red', 'blue', 'green', 'yellow', 'cyan', 'purple', 'orange', 'pink', 'black', 'white'];
    if (cssColors.includes(color.toLowerCase())) return true;

    return false;
  }

  /**
   * 确保颜色映射值和显示颜色的一致性
   */
  private ensureColorConsistency(cellData: CellData): CellData {
    // 这里可以实现颜色映射值和显示颜色的同步逻辑
    // 例如：根据colorMappingValue计算对应的color值
    
    const processedData = { ...cellData };
    
    // 如果colorMappingValue发生变化，重新计算color
    if (cellData.colorMappingValue !== undefined) {
      // 这里可以添加具体的颜色映射算法
      // processedData.color = this.calculateColorFromMappingValue(cellData.colorMappingValue);
    }

    return processedData;
  }

  // ==================== 事件管理 ====================

  /**
   * 添加事件监听器
   */
  public addEventListener(listener: CellDataEventListener): void {
    this.eventListeners.push(listener);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(listener: CellDataEventListener): void {
    const index = this.eventListeners.indexOf(listener);
    if (index > -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(event: CellDataUpdateEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        logger.error('事件监听器执行失败', error);
      }
    });
  }

  // ==================== 配置管理 ====================

  /**
   * 启用/禁用数据验证
   */
  public setValidationEnabled(enabled: boolean): void {
    this.validationEnabled = enabled;
    logger.info(`数据验证已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 获取所有单元格数据
   */
  public getAllCells(): CellData[] {
    return Array.from(this.cellDataMap.values()).map(cell => ({ ...cell }));
  }

  /**
   * 清空所有数据
   */
  public clearAllData(): void {
    this.cellDataMap.clear();
    logger.info('已清空所有单元格数据');
  }

  /**
   * 获取数据统计信息
   */
  public getStatistics(): {
    totalCells: number;
    groupCounts: Record<number, number>;
    levelCounts: Record<number, number>;
  } {
    const groupCounts: Record<number, number> = {};
    const levelCounts: Record<number, number> = {};

    for (const cell of this.cellDataMap.values()) {
      groupCounts[cell.group] = (groupCounts[cell.group] || 0) + 1;
      levelCounts[cell.level] = (levelCounts[cell.level] || 0) + 1;
    }

    return {
      totalCells: this.cellDataMap.size,
      groupCounts,
      levelCounts
    };
  }
}

// 导出单例实例
export const cellDataManager = CellDataManager.getInstance();

// 导出类型
export { CellDataManager };
